/* Chart Export Styles - Safe colors for html2canvas */

/* Override any oklch colors with safe RGB equivalents */
.chart-export-container {
  background-color: #f8fafc !important; /* Safe gray-50 */
  border-radius: 8px !important;
  padding: 16px !important;
  font-family: Arial, sans-serif !important;
}

.chart-export-container * {
  /* Reset any potentially problematic colors */
  color: #374151 !important; /* Safe gray-700 */
}

.chart-export-container canvas {
  background-color: transparent !important;
}

/* Safe color palette for charts */
.chart-safe-colors {
  --chart-blue: #3b82f6;
  --chart-green: #10b981;
  --chart-red: #ef4444;
  --chart-yellow: #f59e0b;
  --chart-purple: #8b5cf6;
  --chart-pink: #ec4899;
  --chart-indigo: #6366f1;
  --chart-gray: #6b7280;
}

/* Button styles for save functionality */
.chart-save-button {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
}

.chart-save-button:hover {
  background-color: #2563eb !important;
}

.chart-save-button:disabled {
  background-color: #9ca3af !important;
  cursor: not-allowed !important;
}

/* Loading spinner */
.chart-save-spinner {
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Ensure no oklch colors are used */
* {
  /* Override any oklch usage with safe alternatives */
  background-color: var(--fallback-bg, inherit) !important;
  color: var(--fallback-text, inherit) !important;
}

/* Chart container specific overrides */
.chart-container {
  --fallback-bg: #ffffff;
  --fallback-text: #374151;
}

.chart-container .bg-gray-50 {
  background-color: #f8fafc !important;
}

.chart-container .text-gray-700 {
  color: #374151 !important;
}

.chart-container .bg-blue-600 {
  background-color: #3b82f6 !important;
}

.chart-container .text-white {
  color: #ffffff !important;
}

/* Remove any CSS custom properties that might use oklch */
.no-oklch * {
  background: unset !important;
  color: unset !important;
}

.no-oklch {
  background-color: #ffffff !important;
  color: #000000 !important;
}
