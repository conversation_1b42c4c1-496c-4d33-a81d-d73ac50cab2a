'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  user_id: string;
  username: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (username: string, password: string) => Promise<boolean>;
  register: (username: string, password: string, confirmPassword: string) => Promise<boolean>;
  logout: () => void;
  isLoading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check for existing token on mount
  useEffect(() => {
    const savedToken = localStorage.getItem('token');
    const savedUser = localStorage.getItem('user');
    
    if (savedToken && savedUser) {
      setToken(savedToken);
      setUser(JSON.parse(savedUser));
    }
    setIsLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const API_URL = process.env.NODE_ENV === 'production'
        ? '/api/auth/login'
        : 'http://localhost:8000/api/auth/login';

      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (response.ok) {
        const data = await response.json();
        const userData = {
          user_id: data.user_id,
          username: data.username,
        };

        setToken(data.access_token);
        setUser(userData);

        // Save to localStorage
        localStorage.setItem('token', data.access_token);
        localStorage.setItem('user', JSON.stringify(userData));

        return true;
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Login failed' }));
        setError(errorData.detail || 'Login failed');
        return false;
      }
    } catch (err) {
      console.error('Login error:', err);
      setError('Network error. Please check if the backend server is running.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (username: string, password: string, confirmPassword: string): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const API_URL = process.env.NODE_ENV === 'production'
        ? '/api/auth/register'
        : 'http://localhost:8000/api/auth/register';

      const response = await fetch(API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
          confirm_password: confirmPassword
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const userData = {
          user_id: data.user_id,
          username: data.username,
        };

        setToken(data.access_token);
        setUser(userData);

        // Save to localStorage
        localStorage.setItem('token', data.access_token);
        localStorage.setItem('user', JSON.stringify(userData));

        return true;
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Registration failed' }));
        setError(errorData.detail || 'Registration failed');
        return false;
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError('Network error. Please check if the backend server is running.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    setError(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('sessionId'); // Clear session data too
  };

  const value = {
    user,
    token,
    login,
    register,
    logout,
    isLoading,
    error,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
