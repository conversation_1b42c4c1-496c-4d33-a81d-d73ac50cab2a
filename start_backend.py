#!/usr/bin/env python3
"""
Simple script to start the Data Science Platform backend server.
This script checks dependencies and starts the server with proper configuration.
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_backend_directory():
    """Check if we're in the right directory."""
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found.")
        print("Please run this script from the project root directory.")
        return False
    
    main_py = backend_dir / "main.py"
    if not main_py.exists():
        print("❌ main.py not found in backend directory.")
        return False
    
    print("✅ Backend directory found.")
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True, cwd=".")
        print("✅ Dependencies installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def start_server():
    """Start the FastAPI server."""
    print("🚀 Starting backend server...")
    print("Server will be available at: http://localhost:8000")
    print("API documentation: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server.")
    print("-" * 50)
    
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", "main:app", 
            "--reload", "--host", "0.0.0.0", "--port", "8000"
        ], cwd="backend")
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start server: {e}")
        return False
    except FileNotFoundError:
        print("❌ uvicorn not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "uvicorn"], check=True)
        print("✅ uvicorn installed. Please run the script again.")
        return False
    
    return True

def main():
    """Main function to start the backend server."""
    print("🔧 Data Science Platform - Backend Startup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_backend_directory():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Start server
    return start_server()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
