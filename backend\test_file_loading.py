#!/usr/bin/env python3
"""
Test script to verify file loading and session management works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import db
from app.utils.session_manager import get_active_file_id, get_session_data
from app.utils.file_handler import get_dataframe, list_files

def test_database_connection():
    """Test database connection and table structure."""
    print("🔍 Testing database connection...")
    try:
        conn = db.get_connection()
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"✅ Available tables: {tables}")
        
        # Check if required tables exist
        required_tables = ['users', 'sessions', 'files', 'saved_visuals']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Missing tables: {missing_tables}")
            return False
        else:
            print("✅ All required tables exist")
            
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_user_sessions():
    """Test user sessions and file records."""
    print("\n🔍 Testing user sessions and files...")
    try:
        # Get all users
        conn = db.get_connection()
        cursor = conn.execute("SELECT user_id, username FROM users")
        users = [dict(row) for row in cursor.fetchall()]
        print(f"✅ Found {len(users)} users")
        
        for user in users:
            user_id = user['user_id']
            username = user['username']
            print(f"\n👤 User: {username} ({user_id})")
            
            # Get user sessions
            cursor = conn.execute("SELECT session_id, active_file_id FROM sessions WHERE user_id = ?", (user_id,))
            sessions = [dict(row) for row in cursor.fetchall()]
            print(f"  📁 Sessions: {len(sessions)}")
            
            for session in sessions:
                session_id = session['session_id']
                active_file_id = session['active_file_id']
                print(f"    Session: {session_id[:8]}... (active_file: {active_file_id[:8] if active_file_id else 'None'}...)")
                
                # Test file listing
                files = list_files(session_id)
                print(f"    Files found: {len(files)}")
                
                for file_info in files:
                    file_id = file_info['file_id']
                    filename = file_info['filename']
                    print(f"      📄 {filename} ({file_id[:8]}...)")
                    
                    # Test dataframe loading
                    df = get_dataframe(session_id, file_id)
                    if df is not None:
                        print(f"        ✅ Dataframe loaded: {df.shape}")
                    else:
                        print(f"        ❌ Failed to load dataframe")
                
                # Test active file retrieval
                active_file_id_retrieved = get_active_file_id(session_id)
                print(f"    Active file ID: {active_file_id_retrieved[:8] if active_file_id_retrieved else 'None'}...")
        
        conn.close()
        return True
    except Exception as e:
        print(f"❌ User session test failed: {e}")
        return False

def test_file_paths():
    """Test if file paths exist on disk."""
    print("\n🔍 Testing file paths on disk...")
    try:
        conn = db.get_connection()
        cursor = conn.execute("SELECT file_id, filename, file_path FROM files")
        files = [dict(row) for row in cursor.fetchall()]
        
        print(f"✅ Found {len(files)} file records in database")
        
        existing_files = 0
        missing_files = 0
        
        for file_record in files:
            file_path = file_record['file_path']
            filename = file_record['filename']
            
            if os.path.exists(file_path):
                existing_files += 1
                print(f"  ✅ {filename} -> {file_path}")
            else:
                missing_files += 1
                print(f"  ❌ {filename} -> {file_path} (MISSING)")
        
        print(f"\n📊 Summary: {existing_files} existing, {missing_files} missing")
        conn.close()
        return missing_files == 0
    except Exception as e:
        print(f"❌ File path test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting file loading tests...\n")
    
    tests = [
        test_database_connection,
        test_user_sessions,
        test_file_paths
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! File loading should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
