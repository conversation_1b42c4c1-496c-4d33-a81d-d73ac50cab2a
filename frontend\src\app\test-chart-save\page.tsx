'use client';

import React, { useState } from 'react';
import { ChartDisplay } from '@/components/data/ChartDisplay';

export default function TestChartSavePage() {
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Sample chart data for testing
  const sampleChartData = {
    chart_type: 'bar',
    chart_data: {
      x: ['A', 'B', 'C', 'D'],
      y: {
        'Sales': [100, 200, 150, 300],
        'Profit': [20, 40, 30, 60]
      }
    }
  };

  const sampleChartConfig = {
    x_column: 'Category',
    y_columns: ['Sales', 'Profit'],
    group_by: '',
    aggregation: 'sum'
  };

  const handleSaveChart = (success: boolean, message: string) => {
    setSaveMessage({ type: success ? 'success' : 'error', message });
    // Clear message after 5 seconds
    setTimeout(() => setSaveMessage(null), 5000);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="bg-gradient-to-r from-purple-600 to-indigo-700 rounded-lg shadow-md p-6 mb-8">
        <div className="max-w-3xl mx-auto text-center">
          <h1 className="text-3xl font-bold text-white">Chart Save Test</h1>
          <p className="mt-2 text-lg text-purple-100">
            Test the chart saving functionality to the visual library
          </p>
        </div>
      </div>

      {/* Save message */}
      {saveMessage && (
        <div className={`border-l-4 p-4 rounded-r-md shadow-sm ${
          saveMessage.type === 'success' 
            ? 'bg-green-50 border-green-400' 
            : 'bg-red-50 border-red-400'
        }`}>
          <div className="flex items-center">
            {saveMessage.type === 'success' ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
            <p className={`text-sm font-medium ${
              saveMessage.type === 'success' ? 'text-green-700' : 'text-red-700'
            }`}>
              {saveMessage.message}
            </p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Instructions */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Instructions</h2>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start">
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mr-3 mt-0.5">1</span>
              <p>Make sure you're logged in to the application</p>
            </div>
            <div className="flex items-start">
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mr-3 mt-0.5">2</span>
              <p>Click the "Save Chart" button on the sample chart</p>
            </div>
            <div className="flex items-start">
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mr-3 mt-0.5">3</span>
              <p>Check for success/error messages above</p>
            </div>
            <div className="flex items-start">
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded mr-3 mt-0.5">4</span>
              <p>Visit the <a href="/library" className="text-blue-600 hover:underline">Visual Library</a> to see saved charts</p>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">Troubleshooting</h3>
            <ul className="text-xs text-yellow-700 space-y-1">
              <li>• If you get "oklch" errors, the new CSS fixes should resolve them</li>
              <li>• If Chart.js methods fail, html2canvas fallback will be used</li>
              <li>• If all methods fail, a text-based summary image will be created</li>
              <li>• Check browser console for detailed error messages</li>
            </ul>
          </div>
        </div>

        {/* Sample Chart */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Sample Chart</h2>
          <ChartDisplay
            chartType={sampleChartData.chart_type}
            chartData={sampleChartData.chart_data}
            chartConfig={sampleChartConfig}
            sessionId="test-session-123"
            onSave={handleSaveChart}
          />
        </div>
      </div>

      {/* Chart Types Test */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Multiple Chart Types Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Line Chart */}
          <div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">Line Chart</h3>
            <ChartDisplay
              chartType="line"
              chartData={sampleChartData.chart_data}
              chartConfig={sampleChartConfig}
              sessionId="test-session-123"
              onSave={handleSaveChart}
            />
          </div>

          {/* Pie Chart */}
          <div>
            <h3 className="text-lg font-medium text-gray-700 mb-2">Pie Chart</h3>
            <ChartDisplay
              chartType="pie"
              chartData={{
                labels: ['A', 'B', 'C', 'D'],
                values: [100, 200, 150, 300]
              }}
              chartConfig={{
                x_column: 'Category',
                y_columns: ['Sales'],
                group_by: '',
                aggregation: 'sum'
              }}
              sessionId="test-session-123"
              onSave={handleSaveChart}
            />
          </div>
        </div>
      </div>

      {/* Debug Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Debug Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-700">Browser Support</h3>
            <ul className="mt-2 space-y-1 text-gray-600">
              <li>Canvas API: {typeof HTMLCanvasElement !== 'undefined' ? '✅' : '❌'}</li>
              <li>html2canvas: {typeof window !== 'undefined' ? '✅' : '❌'}</li>
              <li>Chart.js: {typeof ChartDisplay !== 'undefined' ? '✅' : '❌'}</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-700">Authentication</h3>
            <ul className="mt-2 space-y-1 text-gray-600">
              <li>Token: {typeof window !== 'undefined' && localStorage.getItem('token') ? '✅' : '❌'}</li>
              <li>Session: {typeof window !== 'undefined' && localStorage.getItem('sessionId') ? '✅' : '❌'}</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-700">API Endpoints</h3>
            <ul className="mt-2 space-y-1 text-gray-600">
              <li>Save: /api/library/save</li>
              <li>Library: /api/library/library</li>
              <li>Download: /api/library/download</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
