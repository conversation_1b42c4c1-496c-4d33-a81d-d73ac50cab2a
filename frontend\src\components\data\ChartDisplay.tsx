'use client';

import React, { useRef, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  ScatterController,
  BubbleController,
  RadialLinearScale,
} from 'chart.js';
import { Bar, Line, Pie, Scatter, Bubble } from 'react-chartjs-2';
import { HeatMapGrid } from '@/components/data/HeatMapGrid';
import html2canvas from 'html2canvas';
import '@/styles/chart-export.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  ScatterController,
  BubbleController,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend
);

interface ChartDisplayProps {
  chartType: string;
  chartData: any;
  chartConfig: {
    x_column: string;
    y_columns: string[];
    group_by?: string;
    aggregation?: string;
  };
  sessionId?: string;
  onSave?: (success: boolean, message: string) => void;
}

export function ChartDisplay({ chartType, chartData, chartConfig, sessionId, onSave }: ChartDisplayProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<any>(null);
  const [isSaving, setIsSaving] = useState(false);

  const saveChart = async () => {
    if (!chartRef.current || !sessionId) {
      onSave?.(false, 'Unable to save chart');
      return;
    }

    setIsSaving(true);
    try {
      let imageData: string;

      // Method 1: Try to get canvas directly from Chart.js
      if (chartInstanceRef.current?.canvas) {
        try {
          const canvas = chartInstanceRef.current.canvas;
          imageData = canvas.toDataURL('image/png');
          console.log('✅ Used Chart.js canvas method');
        } catch (chartError) {
          console.warn('Chart.js canvas method failed, trying toBase64Image:', chartError);
          // Method 2: Try Chart.js toBase64Image
          if (typeof chartInstanceRef.current.toBase64Image === 'function') {
            try {
              imageData = chartInstanceRef.current.toBase64Image('image/png', 1.0);
              console.log('✅ Used Chart.js toBase64Image method');
            } catch (base64Error) {
              console.warn('Chart.js toBase64Image failed, falling back to html2canvas:', base64Error);
              imageData = await captureWithHtml2Canvas();
            }
          } else {
            imageData = await captureWithHtml2Canvas();
          }
        }
      } else {
        // Method 3: Fallback to html2canvas with enhanced error handling
        imageData = await captureWithHtml2Canvas();
      }

      // Generate chart title
      const title = `${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart - ${chartConfig.x_column} vs ${chartConfig.y_columns.join(', ')}`;

      // Save to backend
      const token = localStorage.getItem('token');
      const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/library/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          chart_type: chartType,
          chart_config: chartConfig,
          image_data: imageData,
          session_id: sessionId,
        }),
      });

      if (response.ok) {
        onSave?.(true, 'Chart saved to library successfully!');
      } else {
        const error = await response.json();
        onSave?.(false, error.detail || 'Failed to save chart');
      }
    } catch (error) {
      console.error('Error saving chart:', error);

      // Last resort: Create a simple text-based image
      try {
        const fallbackImage = createFallbackImage();
        const title = `${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart - ${chartConfig.x_column} vs ${chartConfig.y_columns.join(', ')}`;

        const token = localStorage.getItem('token');
        const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api';
        const response = await fetch(`${API_BASE_URL}/library/save`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            title,
            chart_type: chartType,
            chart_config: chartConfig,
            image_data: fallbackImage,
            session_id: sessionId,
          }),
        });

        if (response.ok) {
          onSave?.(true, 'Chart saved to library (as text summary)');
        } else {
          onSave?.(false, 'Failed to save chart');
        }
      } catch (fallbackError) {
        onSave?.(false, 'Error saving chart: ' + (error as Error).message);
      }
    } finally {
      setIsSaving(false);
    }
  };

      // Generate chart title
      const title = `${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart - ${chartConfig.x_column} vs ${chartConfig.y_columns.join(', ')}`;

      // Save to backend
      const token = localStorage.getItem('token');
      const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api';
      const response = await fetch(`${API_BASE_URL}/library/save`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          chart_type: chartType,
          chart_config: chartConfig,
          image_data: imageData,
          session_id: sessionId,
        }),
      });

      if (response.ok) {
        onSave?.(true, 'Chart saved to library successfully!');
      } else {
        const error = await response.json();
        onSave?.(false, error.detail || 'Failed to save chart');
      }
    } catch (error) {
      console.error('Error saving chart:', error);

      // Last resort: Create a simple text-based image
      try {
        const fallbackImage = createFallbackImage();
        const title = `${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart - ${chartConfig.x_column} vs ${chartConfig.y_columns.join(', ')}`;

        const token = localStorage.getItem('token');
        const API_BASE_URL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:8000/api';
        const response = await fetch(`${API_BASE_URL}/library/save`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            title,
            chart_type: chartType,
            chart_config: chartConfig,
            image_data: fallbackImage,
            session_id: sessionId,
          }),
        });

        if (response.ok) {
          onSave?.(true, 'Chart saved to library (as text summary)');
        } else {
          onSave?.(false, 'Failed to save chart');
        }
      } catch (fallbackError) {
        onSave?.(false, 'Error saving chart: ' + (error as Error).message);
      }
    } finally {
      setIsSaving(false);
    }
  };

  const createFallbackImage = (): string => {
    // Create a simple canvas with chart information
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 400;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // Background
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, 800, 400);

      // Border
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 2;
      ctx.strokeRect(10, 10, 780, 380);

      // Title
      ctx.fillStyle = '#374151';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(`${chartType.toUpperCase()} CHART`, 400, 60);

      // Chart info
      ctx.font = '16px Arial';
      ctx.fillText(`X-Axis: ${chartConfig.x_column}`, 400, 120);
      ctx.fillText(`Y-Axis: ${chartConfig.y_columns.join(', ')}`, 400, 150);
      if (chartConfig.group_by) {
        ctx.fillText(`Grouped by: ${chartConfig.group_by}`, 400, 180);
      }

      // Note
      ctx.font = '14px Arial';
      ctx.fillStyle = '#6b7280';
      ctx.fillText('Chart visualization saved as summary', 400, 250);
      ctx.fillText('(Visual rendering not available)', 400, 270);

      // Timestamp
      ctx.fillText(`Saved: ${new Date().toLocaleString()}`, 400, 350);
    }

    return canvas.toDataURL('image/png');
  };

  const captureWithHtml2Canvas = async (): Promise<string> => {
    if (!chartRef.current) throw new Error('Chart reference not available');

    // Create a clean container for capturing
    const captureContainer = document.createElement('div');
    captureContainer.style.position = 'absolute';
    captureContainer.style.left = '-9999px';
    captureContainer.style.top = '-9999px';
    captureContainer.style.width = '800px';
    captureContainer.style.height = '400px';
    captureContainer.style.backgroundColor = '#ffffff';
    captureContainer.style.padding = '20px';
    captureContainer.style.fontFamily = 'Arial, sans-serif';

    // Clone the chart content
    const chartClone = chartRef.current.cloneNode(true) as HTMLElement;

    // Clean up styles to avoid oklch issues
    const cleanElement = (element: HTMLElement) => {
      // Remove all classes that might contain problematic CSS
      element.className = '';

      // Apply safe inline styles
      if (element.tagName === 'DIV') {
        element.style.backgroundColor = element.style.backgroundColor || '#f8fafc';
        element.style.borderRadius = '8px';
        element.style.padding = '16px';
      }

      // Clean child elements
      Array.from(element.children).forEach(child => {
        cleanElement(child as HTMLElement);
      });
    };

    cleanElement(chartClone);
    captureContainer.appendChild(chartClone);
    document.body.appendChild(captureContainer);

    try {
      const canvas = await html2canvas(captureContainer, {
        backgroundColor: '#ffffff',
        scale: 1.5,
        useCORS: true,
        allowTaint: true,
        logging: false,
        ignoreElements: (element) => {
          // Ignore elements that might cause issues
          return element.tagName === 'SCRIPT' || element.tagName === 'STYLE';
        }
      });

      const imageData = canvas.toDataURL('image/png');
      console.log('✅ Used html2canvas method');
      return imageData;
    } finally {
      // Always clean up
      document.body.removeChild(captureContainer);
    }
  };

  const createFallbackImage = (): string => {
    // Create a simple canvas with chart information
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 400;
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // Background
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, 800, 400);

      // Border
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 2;
      ctx.strokeRect(10, 10, 780, 380);

      // Title
      ctx.fillStyle = '#374151';
      ctx.font = 'bold 24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(`${chartType.toUpperCase()} CHART`, 400, 60);

      // Chart info
      ctx.font = '16px Arial';
      ctx.fillText(`X-Axis: ${chartConfig.x_column}`, 400, 120);
      ctx.fillText(`Y-Axis: ${chartConfig.y_columns.join(', ')}`, 400, 150);
      if (chartConfig.group_by) {
        ctx.fillText(`Grouped by: ${chartConfig.group_by}`, 400, 180);
      }

      // Note
      ctx.font = '14px Arial';
      ctx.fillStyle = '#6b7280';
      ctx.fillText('Chart visualization saved as summary', 400, 250);
      ctx.fillText('(Visual rendering not available)', 400, 270);

      // Timestamp
      ctx.fillText(`Saved: ${new Date().toLocaleString()}`, 400, 350);
    }

    return canvas.toDataURL('image/png');
  };

  if (!chartData || !chartType) {
    return (
      <div className="h-96 bg-gray-50 p-4 rounded-lg flex items-center justify-center">
        <p className="text-gray-400">
          Configure and generate a chart to see the visualization
        </p>
      </div>
    );
  }

  // Extract data for the chart
  const getChartData = () => {
    // Default colors for datasets
    const colors = [
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(75, 192, 192, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
    ];

    // For simple bar/line charts
    if ((chartType === 'bar' || chartType === 'line') && chartData.chart_data) {
      // Handle different data structures
      if (chartData.chart_data.x && chartData.chart_data.y) {
        // Simple x and y structure
        const labels = chartData.chart_data.x;
        const datasets = Object.entries(chartData.chart_data.y).map(([key, values], index) => ({
          label: key,
          data: values as number[],
          backgroundColor: colors[index % colors.length],
          borderColor: colors[index % colors.length].replace('0.6', '1'),
          borderWidth: 1,
        }));

        return { labels, datasets };
      } else if (Array.isArray(chartData.chart_data)) {
        // Array of records
        const labels = chartData.chart_data.map((item: any) => item[chartConfig.x_column]);
        const datasets = chartConfig.y_columns.map((column, index) => ({
          label: column,
          data: chartData.chart_data.map((item: any) => item[column]),
          backgroundColor: colors[index % colors.length],
          borderColor: colors[index % colors.length].replace('0.6', '1'),
          borderWidth: 1,
        }));

        return { labels, datasets };
      }
    }

    // For pie charts
    if (chartType === 'pie' && chartData.chart_data) {
      if (chartData.chart_data.labels && chartData.chart_data.values) {
        return {
          labels: chartData.chart_data.labels,
          datasets: [
            {
              data: chartData.chart_data.values,
              backgroundColor: colors.slice(0, chartData.chart_data.labels.length),
              borderColor: colors.map(color => color.replace('0.6', '1')).slice(0, chartData.chart_data.labels.length),
              borderWidth: 1,
            },
          ],
        };
      }
    }

    // For histogram
    if (chartType === 'histogram' && chartData.chart_data) {
      if (chartData.chart_data.data && chartData.chart_data.bins) {
        // Create labels from bin edges (use middle of each bin)
        const binEdges = chartData.chart_data.bins;
        const labels = [];
        for (let i = 0; i < binEdges.length - 1; i++) {
          const binMiddle = (binEdges[i] + binEdges[i + 1]) / 2;
          labels.push(binMiddle.toFixed(2));
        }

        return {
          labels,
          datasets: [
            {
              label: 'Frequency',
              data: chartData.chart_data.data,
              backgroundColor: colors[0],
              borderColor: colors[0].replace('0.6', '1'),
              borderWidth: 1,
            },
          ],
        };
      } else if (chartData.chart_data.labels && chartData.chart_data.values) {
        // For categorical histograms
        return {
          labels: chartData.chart_data.labels,
          datasets: [
            {
              label: 'Frequency',
              data: chartData.chart_data.values,
              backgroundColor: colors[0],
              borderColor: colors[0].replace('0.6', '1'),
              borderWidth: 1,
            },
          ],
        };
      }
    }

    // For scatter plots
    if (chartType === 'scatter' && chartData.chart_data) {
      if (chartData.chart_data.data) {
        // Handle multiple series
        return {
          datasets: chartData.chart_data.data.map((series: any, index: number) => ({
            label: series.name || `Series ${index + 1}`,
            data: series.x.map((x: number, i: number) => ({ x, y: series.y[i] })),
            backgroundColor: colors[index % colors.length],
            borderColor: colors[index % colors.length].replace('0.6', '1'),
            pointRadius: series.size ? series.size.map((s: number) => Math.max(3, s / 5)) : 5,
            pointHoverRadius: 7,
          })),
        };
      }
    }

    // Default empty data
    return {
      labels: [],
      datasets: [
        {
          label: 'No data',
          data: [],
          backgroundColor: colors[0],
          borderColor: colors[0].replace('0.6', '1'),
          borderWidth: 1,
        },
      ],
    };
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: `${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart`,
        font: {
          size: 16,
        },
      },
      tooltip: {
        enabled: true,
      },
    },
  };

  // Render the appropriate chart based on type
  const renderChart = () => {
    const data = getChartData();

    // Special handling for heatmap
    if (chartType === 'heatmap' && chartData.chart_data) {
      if (chartData.chart_data.x && chartData.chart_data.y && chartData.chart_data.z) {
        return (
          <HeatMapGrid
            data={chartData.chart_data.z}
            xLabels={chartData.chart_data.x}
            yLabels={chartData.chart_data.y}
          />
        );
      }
      return (
        <div className="text-center">
          <p className="text-lg font-medium text-gray-700">
            Heatmap
          </p>
          <p className="text-sm text-gray-500">
            Invalid data format for heatmap
          </p>
        </div>
      );
    }

    switch (chartType) {
      case 'bar':
        return <Bar
          data={data}
          options={options}
          ref={(ref: any) => { chartInstanceRef.current = ref; }}
        />;
      case 'line':
        return <Line
          data={data}
          options={options}
          ref={(ref: any) => { chartInstanceRef.current = ref; }}
        />;
      case 'pie':
        return <Pie
          data={data}
          options={options}
          ref={(ref: any) => { chartInstanceRef.current = ref; }}
        />;
      case 'histogram':
        // Histograms are rendered as bar charts
        return <Bar
          data={data}
          options={{
            ...options,
            scales: {
              x: {
                title: {
                  display: true,
                  text: chartConfig.x_column || 'Value'
                }
              },
              y: {
                title: {
                  display: true,
                  text: 'Frequency'
                }
              }
            }
          }}
          ref={(ref: any) => { chartInstanceRef.current = ref; }}
        />;
      case 'scatter':
        return <Scatter
          data={data}
          options={{
            ...options,
            scales: {
              x: {
                title: {
                  display: true,
                  text: chartConfig.x_column || 'X'
                }
              },
              y: {
                title: {
                  display: true,
                  text: chartConfig.y_columns[0] || 'Y'
                }
              }
            }
          }}
          ref={(ref: any) => { chartInstanceRef.current = ref; }}
        />;
      default:
        return (
          <div className="text-center">
            <p className="text-lg font-medium text-gray-700">
              {chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart
            </p>
            <p className="text-sm text-gray-500">
              Chart type not supported for visualization
            </p>
          </div>
        );
    }
  };

  return (
    <div className="relative">
      {/* Save Button */}
      {sessionId && (
        <div className="absolute top-2 right-2 z-10">
          <button
            onClick={saveChart}
            disabled={isSaving}
            className="chart-save-button bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-3 py-1 rounded-md text-sm font-medium shadow-md transition-colors duration-200 flex items-center gap-2"
          >
            {isSaving ? (
              <>
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Save Chart
              </>
            )}
          </button>
        </div>
      )}

      {/* Chart Container */}
      <div ref={chartRef} className="h-96 bg-gray-50 p-4 rounded-lg chart-container chart-export-container">
        {renderChart()}
      </div>
    </div>
  );
}

export default ChartDisplay;
