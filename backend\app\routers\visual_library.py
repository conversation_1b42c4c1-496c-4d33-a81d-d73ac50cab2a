from fastapi import APIRouter, HTTPException, Depends, Body, File, UploadFile
from fastapi.responses import JSONResponse, FileResponse
from typing import Optional, Dict, Any, List
import os
import uuid
import json
import base64
from datetime import datetime
from ..utils.session_manager import get_session_data
from ..auth.auth import get_current_user
from ..database.database import db

router = APIRouter()

@router.post("/save")
async def save_visual(
    visual_data: Dict[str, Any] = Body(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Save a chart/visual to the user's library.
    Expected payload:
    {
        "title": "Chart Title",
        "chart_type": "bar",
        "chart_config": {...},
        "image_data": "base64_encoded_image",
        "session_id": "session_id"
    }
    """
    try:
        # Extract data from request
        title = visual_data.get("title", "Untitled Chart")
        chart_type = visual_data.get("chart_type")
        chart_config = visual_data.get("chart_config", {})
        image_data = visual_data.get("image_data")
        session_id = visual_data.get("session_id")
        
        if not chart_type or not image_data:
            raise HTTPException(status_code=400, detail="Chart type and image data are required")
        
        # Generate unique visual ID
        visual_id = str(uuid.uuid4())
        
        # Create user visual directory structure
        user_id = current_user["user_id"]
        current_date = datetime.now().strftime("%Y-%m-%d")
        visual_dir = os.path.join("data", user_id, "visuals", current_date)
        os.makedirs(visual_dir, exist_ok=True)
        
        # Save the image file
        image_filename = f"{visual_id}.png"
        image_path = os.path.join(visual_dir, image_filename)
        
        # Decode base64 image and save
        try:
            # Remove data URL prefix if present
            if image_data.startswith('data:image'):
                image_data = image_data.split(',')[1]
            
            image_bytes = base64.b64decode(image_data)
            with open(image_path, 'wb') as f:
                f.write(image_bytes)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid image data: {str(e)}")
        
        # Create thumbnail (optional - for now we'll use the same image)
        thumbnail_path = image_path
        
        # Save to database
        db.save_visual(
            visual_id=visual_id,
            user_id=user_id,
            session_id=session_id,
            title=title,
            chart_type=chart_type,
            chart_config=json.dumps(chart_config),
            image_path=image_path,
            thumbnail_path=thumbnail_path
        )
        
        return JSONResponse(content={
            "message": "Visual saved successfully",
            "visual_id": visual_id,
            "title": title,
            "chart_type": chart_type
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving visual: {str(e)}")

@router.get("/library")
async def get_visual_library(
    limit: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get user's saved visuals library"""
    try:
        user_id = current_user["user_id"]
        visuals = db.get_user_visuals(user_id, limit)
        
        # Process visuals to include additional metadata
        processed_visuals = []
        for visual in visuals:
            # Parse chart_config back to dict
            try:
                chart_config = json.loads(visual["chart_config"])
            except:
                chart_config = {}
            
            processed_visual = {
                "visual_id": visual["visual_id"],
                "title": visual["title"],
                "chart_type": visual["chart_type"],
                "chart_config": chart_config,
                "created_at": visual["created_at"],
                "session_id": visual["session_id"],
                "has_thumbnail": visual["thumbnail_path"] is not None
            }
            processed_visuals.append(processed_visual)
        
        return JSONResponse(content={
            "visuals": processed_visuals,
            "total": len(processed_visuals)
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving visual library: {str(e)}")

@router.get("/download/{visual_id}")
async def download_visual(
    visual_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Download a specific visual"""
    try:
        user_id = current_user["user_id"]
        visual = db.get_visual_by_id(visual_id, user_id)
        
        if not visual:
            raise HTTPException(status_code=404, detail="Visual not found")
        
        image_path = visual["image_path"]
        if not os.path.exists(image_path):
            raise HTTPException(status_code=404, detail="Visual file not found")
        
        # Return the file for download
        filename = f"{visual['title'].replace(' ', '_')}_{visual['chart_type']}.png"
        return FileResponse(
            path=image_path,
            filename=filename,
            media_type='image/png'
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error downloading visual: {str(e)}")

@router.get("/thumbnail/{visual_id}")
async def get_visual_thumbnail(
    visual_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get thumbnail for a specific visual"""
    try:
        user_id = current_user["user_id"]
        visual = db.get_visual_by_id(visual_id, user_id)
        
        if not visual:
            raise HTTPException(status_code=404, detail="Visual not found")
        
        thumbnail_path = visual["thumbnail_path"] or visual["image_path"]
        if not os.path.exists(thumbnail_path):
            raise HTTPException(status_code=404, detail="Thumbnail not found")
        
        return FileResponse(
            path=thumbnail_path,
            media_type='image/png'
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving thumbnail: {str(e)}")

@router.delete("/{visual_id}")
async def delete_visual(
    visual_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Delete a saved visual"""
    try:
        user_id = current_user["user_id"]
        visual = db.get_visual_by_id(visual_id, user_id)
        
        if not visual:
            raise HTTPException(status_code=404, detail="Visual not found")
        
        # Delete the file from disk
        image_path = visual["image_path"]
        if os.path.exists(image_path):
            os.remove(image_path)
        
        # Delete thumbnail if different from main image
        thumbnail_path = visual["thumbnail_path"]
        if thumbnail_path and thumbnail_path != image_path and os.path.exists(thumbnail_path):
            os.remove(thumbnail_path)
        
        # Delete from database
        success = db.delete_visual(visual_id, user_id)
        
        if success:
            return JSONResponse(content={"message": "Visual deleted successfully"})
        else:
            raise HTTPException(status_code=404, detail="Visual not found")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting visual: {str(e)}")

@router.get("/stats")
async def get_library_stats(
    current_user: dict = Depends(get_current_user)
):
    """Get statistics about user's visual library"""
    try:
        user_id = current_user["user_id"]
        visuals = db.get_user_visuals(user_id)
        
        # Calculate statistics
        total_visuals = len(visuals)
        chart_types = {}
        
        for visual in visuals:
            chart_type = visual["chart_type"]
            chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
        
        return JSONResponse(content={
            "total_visuals": total_visuals,
            "chart_types": chart_types,
            "most_used_chart": max(chart_types.items(), key=lambda x: x[1])[0] if chart_types else None
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving library stats: {str(e)}")
