#!/usr/bin/env python3
"""
Test script to verify the Data Science Platform setup.
"""

import requests
import time
import sys
from pathlib import Path

def test_backend_connection():
    """Test if backend server is running and responding."""
    print("🔍 Testing backend connection...")
    
    try:
        response = requests.get("http://localhost:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running and responding.")
            return True
        else:
            print(f"❌ Backend server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Is it running on port 8000?")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend server connection timed out.")
        return False
    except Exception as e:
        print(f"❌ Error connecting to backend: {e}")
        return False

def test_auth_endpoints():
    """Test authentication endpoints."""
    print("🔍 Testing authentication endpoints...")
    
    try:
        # Test register endpoint
        response = requests.post("http://localhost:8000/api/auth/register", 
                               json={"username": "test", "password": "test123", "confirm_password": "test123"},
                               timeout=5)
        
        if response.status_code in [200, 400]:  # 400 might be "user already exists"
            print("✅ Register endpoint is accessible.")
        else:
            print(f"❌ Register endpoint returned: {response.status_code}")
            return False
            
        # Test login endpoint
        response = requests.post("http://localhost:8000/api/auth/login",
                               json={"username": "test", "password": "test123"},
                               timeout=5)
        
        if response.status_code in [200, 401]:  # 401 might be "invalid credentials"
            print("✅ Login endpoint is accessible.")
        else:
            print(f"❌ Login endpoint returned: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing auth endpoints: {e}")
        return False

def test_file_structure():
    """Test if required files and directories exist."""
    print("🔍 Testing file structure...")
    
    required_files = [
        "backend/main.py",
        "backend/requirements.txt",
        "backend/app/auth/auth.py",
        "backend/app/database/database.py",
        "backend/app/models/user.py",
        "backend/app/routers/auth.py",
        "frontend/package.json",
        "frontend/src/app/login/page.tsx",
        "frontend/src/app/register/page.tsx",
        "frontend/src/contexts/AuthContext.tsx"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing required files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files are present.")
        return True

def test_database_creation():
    """Test if database can be created."""
    print("🔍 Testing database creation...")
    
    try:
        # Import database module to trigger database creation
        sys.path.append("backend")
        from app.database.database import db
        
        # Try to create a test user (this will fail if user exists, which is fine)
        try:
            user_id = db.create_user("testuser", "hashedpassword")
            if user_id:
                print("✅ Database operations working.")
                return True
        except Exception:
            # User might already exist, try to get user instead
            user = db.get_user_by_username("testuser")
            if user is not None:
                print("✅ Database operations working.")
                return True
            else:
                print("❌ Database operations failed.")
                return False
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Data Science Platform - Setup Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Database Creation", test_database_creation),
        ("Backend Connection", test_backend_connection),
        ("Authentication Endpoints", test_auth_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"💡 Tip: Check the SETUP.md file for troubleshooting.")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is working correctly.")
        print("🚀 You can now start using the Data Science Platform.")
    else:
        print("⚠️  Some tests failed. Please check the setup instructions.")
        print("📖 See SETUP.md for detailed setup instructions.")
    
    return passed == total

if __name__ == "__main__":
    try:
        import requests
    except ImportError:
        print("❌ 'requests' library not found. Installing...")
        import subprocess
        subprocess.run([sys.executable, "-m", "pip", "install", "requests"], check=True)
        import requests
    
    success = main()
    if not success:
        sys.exit(1)
