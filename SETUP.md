# Data Science Platform - Setup Guide

## Overview
This is a comprehensive data science platform with user authentication, session management, and user-specific data storage. Users can upload CSV/Excel files, perform data analysis, cleaning, machine learning, and visualization.

## Features
- ✅ User authentication (login/register)
- ✅ Session management with SQLite database
- ✅ User-specific data storage in `data/{userid}/` folders
- ✅ Multi-file upload support
- ✅ Data profiling and cleaning
- ✅ Machine learning capabilities
- ✅ Interactive data visualization
- ✅ AI-powered data analysis
- ✅ Beautiful Excel-inspired UI

## Quick Setup

### Backend Setup
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Start the backend server:
   ```bash
   python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup
1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the frontend development server:
   ```bash
   npm run dev
   ```

4. Open your browser and go to: `http://localhost:3000`

## Usage

### First Time Setup
1. **Start Backend**: Make sure the backend server is running on port 8000
2. **Create Account**: Register a new account or login with existing credentials
3. **Upload Data**: Upload CSV or Excel files to start analyzing your data
4. **Explore Features**: Use data profiling, cleaning, machine learning, and visualization tools

### Authentication
- **Register**: Create a new account with username/password
- **Login**: Sign in with your credentials
- **Session Management**: Your session and data are automatically saved
- **User Data**: All uploaded files are stored in your personal folder

### File Management
- **Multi-file Upload**: Upload multiple CSV/Excel files at once
- **User-specific Storage**: Files are stored in `data/{userid}/` directories
- **Session Persistence**: Your work is automatically saved to the database

## Database Structure
The application uses SQLite with the following tables:
- `users`: User accounts and authentication
- `sessions`: User sessions and metadata
- `files`: File records and metadata

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/logout` - Logout user

### File Upload
- `POST /api/upload` - Upload file (anonymous)
- `POST /api/upload/authenticated` - Upload file (authenticated)

### Data Operations
- Data profiling, cleaning, ML, and visualization endpoints
- All require authentication for user-specific operations

## Troubleshooting

### Network Errors
- Ensure backend server is running on port 8000
- Check if there are any firewall issues
- Verify the API_BASE_URL in frontend configuration

### Database Issues
- The SQLite database is automatically created on first run
- Database file: `backend/app.db`
- User data directories are created automatically

### File Upload Issues
- Supported formats: CSV, XLS, XLSX
- Files are stored in `data/{userid}/` directories
- Check file permissions and disk space

## Development Notes
- Backend: FastAPI with SQLite database
- Frontend: Next.js with TypeScript and Tailwind CSS
- Authentication: JWT tokens with bcrypt password hashing
- File Storage: User-specific directories with database metadata
- UI: Excel-inspired design with responsive layout

## Security Features
- Password hashing with bcrypt
- JWT token authentication
- User-specific data isolation
- Session management
- Protected API endpoints

## Next Steps
After setup, you can:
1. Upload your data files
2. Explore data profiling features
3. Clean and transform your data
4. Build machine learning models
5. Create interactive visualizations
6. Use AI-powered analysis tools
