import sqlite3
import os
from typing import Optional, Dict, Any
from datetime import datetime
import uuid
import hashlib

class Database:
    def __init__(self, db_path: str = "app.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # This enables column access by name
        return conn
    
    def init_database(self):
        """Initialize database with required tables"""
        conn = self.get_connection()
        try:
            # Create users table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create sessions table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    session_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    active_file_id TEXT,
                    data TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')
            
            # Create files table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS files (
                    file_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    filename TEXT NOT NULL,
                    file_path TEXT NOT NULL,
                    rows INTEGER,
                    columns INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id),
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            ''')

            # Create saved_visuals table for the visual library
            conn.execute('''
                CREATE TABLE IF NOT EXISTS saved_visuals (
                    visual_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    chart_type TEXT NOT NULL,
                    chart_config TEXT NOT NULL,
                    image_path TEXT NOT NULL,
                    thumbnail_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id),
                    FOREIGN KEY (session_id) REFERENCES sessions (session_id)
                )
            ''')

            conn.commit()
        finally:
            conn.close()
    
    def create_user(self, username: str, password_hash: str) -> str:
        """Create a new user and return user_id"""
        user_id = str(uuid.uuid4())
        conn = self.get_connection()
        try:
            conn.execute(
                "INSERT INTO users (user_id, username, password_hash) VALUES (?, ?, ?)",
                (user_id, username, password_hash)
            )
            conn.commit()
            
            # Create user data directory
            user_data_dir = os.path.join("data", user_id)
            os.makedirs(user_data_dir, exist_ok=True)
            
            return user_id
        finally:
            conn.close()
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        conn = self.get_connection()
        try:
            cursor = conn.execute(
                "SELECT user_id, username, password_hash, created_at FROM users WHERE username = ?",
                (username,)
            )
            row = cursor.fetchone()
            if row:
                return dict(row)
            return None
        finally:
            conn.close()
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by user_id"""
        conn = self.get_connection()
        try:
            cursor = conn.execute(
                "SELECT user_id, username, password_hash, created_at FROM users WHERE user_id = ?",
                (user_id,)
            )
            row = cursor.fetchone()
            if row:
                return dict(row)
            return None
        finally:
            conn.close()
    
    def create_session(self, user_id: str) -> str:
        """Create a new session for user"""
        session_id = str(uuid.uuid4())
        conn = self.get_connection()
        try:
            conn.execute(
                "INSERT INTO sessions (session_id, user_id, data) VALUES (?, ?, ?)",
                (session_id, user_id, "{}")
            )
            conn.commit()
            return session_id
        finally:
            conn.close()
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session by session_id"""
        conn = self.get_connection()
        try:
            cursor = conn.execute(
                "SELECT session_id, user_id, created_at, active_file_id, data FROM sessions WHERE session_id = ?",
                (session_id,)
            )
            row = cursor.fetchone()
            if row:
                return dict(row)
            return None
        finally:
            conn.close()
    
    def update_session(self, session_id: str, **kwargs):
        """Update session data"""
        conn = self.get_connection()
        try:
            # Build dynamic update query
            set_clauses = []
            values = []
            for key, value in kwargs.items():
                if key in ['active_file_id', 'data']:
                    set_clauses.append(f"{key} = ?")
                    values.append(value)
            
            if set_clauses:
                values.append(session_id)
                query = f"UPDATE sessions SET {', '.join(set_clauses)} WHERE session_id = ?"
                conn.execute(query, values)
                conn.commit()
        finally:
            conn.close()
    
    def create_file_record(self, file_id: str, user_id: str, session_id: str, 
                          filename: str, file_path: str, rows: int, columns: int):
        """Create file record in database"""
        conn = self.get_connection()
        try:
            conn.execute(
                """INSERT INTO files (file_id, user_id, session_id, filename, file_path, rows, columns) 
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (file_id, user_id, session_id, filename, file_path, rows, columns)
            )
            conn.commit()
        finally:
            conn.close()
    
    def get_user_files(self, user_id: str, session_id: str = None):
        """Get files for user, optionally filtered by session"""
        conn = self.get_connection()
        try:
            if session_id:
                cursor = conn.execute(
                    """SELECT file_id, filename, file_path, rows, columns, created_at 
                       FROM files WHERE user_id = ? AND session_id = ? ORDER BY created_at DESC""",
                    (user_id, session_id)
                )
            else:
                cursor = conn.execute(
                    """SELECT file_id, filename, file_path, rows, columns, created_at 
                       FROM files WHERE user_id = ? ORDER BY created_at DESC""",
                    (user_id,)
                )
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()

    def save_visual(self, visual_id: str, user_id: str, session_id: str,
                   title: str, chart_type: str, chart_config: str,
                   image_path: str, thumbnail_path: str = None):
        """Save a visual to the library"""
        conn = self.get_connection()
        try:
            conn.execute(
                """INSERT INTO saved_visuals
                   (visual_id, user_id, session_id, title, chart_type, chart_config, image_path, thumbnail_path)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (visual_id, user_id, session_id, title, chart_type, chart_config, image_path, thumbnail_path)
            )
            conn.commit()
        finally:
            conn.close()

    def get_user_visuals(self, user_id: str, limit: int = None):
        """Get saved visuals for a user"""
        conn = self.get_connection()
        try:
            query = """SELECT visual_id, title, chart_type, chart_config, image_path,
                             thumbnail_path, created_at, session_id
                       FROM saved_visuals WHERE user_id = ? ORDER BY created_at DESC"""
            params = [user_id]

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()

    def get_visual_by_id(self, visual_id: str, user_id: str = None):
        """Get a specific visual by ID"""
        conn = self.get_connection()
        try:
            if user_id:
                cursor = conn.execute(
                    """SELECT visual_id, user_id, session_id, title, chart_type, chart_config,
                              image_path, thumbnail_path, created_at
                       FROM saved_visuals WHERE visual_id = ? AND user_id = ?""",
                    (visual_id, user_id)
                )
            else:
                cursor = conn.execute(
                    """SELECT visual_id, user_id, session_id, title, chart_type, chart_config,
                              image_path, thumbnail_path, created_at
                       FROM saved_visuals WHERE visual_id = ?""",
                    (visual_id,)
                )
            row = cursor.fetchone()
            if row:
                return dict(row)
            return None
        finally:
            conn.close()

    def delete_visual(self, visual_id: str, user_id: str):
        """Delete a saved visual"""
        conn = self.get_connection()
        try:
            cursor = conn.execute(
                "DELETE FROM saved_visuals WHERE visual_id = ? AND user_id = ?",
                (visual_id, user_id)
            )
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()

# Global database instance
db = Database()
