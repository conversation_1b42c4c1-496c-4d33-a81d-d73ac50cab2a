#!/usr/bin/env python3
"""
Test script to verify the visualization fix works correctly.
This simulates the upload and chart generation process.
"""

import sys
import os
import pandas as pd
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import db
from app.utils.session_manager import get_active_file_id, set_active_file, add_file_to_session
from app.utils.file_handler import get_dataframe, list_files, save_dataframe

def create_test_data():
    """Create a simple test dataset."""
    data = {
        'Category': ['A', 'B', 'C', 'A', 'B', 'C'],
        'Value1': [10, 20, 30, 15, 25, 35],
        'Value2': [100, 200, 300, 150, 250, 350],
        'Date': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05', '2023-01-06']
    }
    return pd.DataFrame(data)

def test_file_upload_and_visualization():
    """Test the complete flow from file upload to visualization."""
    print("🚀 Testing file upload and visualization flow...\n")
    
    # Step 1: Get a test user and session
    print("1️⃣ Setting up test user and session...")
    try:
        conn = db.get_connection()
        cursor = conn.execute("SELECT user_id, username FROM users LIMIT 1")
        user = cursor.fetchone()
        
        if not user:
            print("❌ No users found in database. Please create a user first.")
            return False
            
        user_id = user['user_id']
        username = user['username']
        print(f"✅ Using test user: {username} ({user_id})")
        
        # Get user's session
        cursor = conn.execute("SELECT session_id FROM sessions WHERE user_id = ? LIMIT 1", (user_id,))
        session = cursor.fetchone()
        
        if not session:
            print("❌ No sessions found for user. Please log in first.")
            return False
            
        session_id = session['session_id']
        print(f"✅ Using session: {session_id[:8]}...")
        
        conn.close()
    except Exception as e:
        print(f"❌ Error setting up test user: {e}")
        return False
    
    # Step 2: Create and store test data
    print("\n2️⃣ Creating and storing test data...")
    try:
        df = create_test_data()
        print(f"✅ Created test dataframe: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        
        # Generate a test file ID
        import uuid
        file_id = str(uuid.uuid4())
        
        # Store the dataframe (simulating file upload)
        file_id = save_dataframe(df, session_id, "test_data.csv", file_id, user_id)
        print(f"✅ Stored dataframe with file_id: {file_id[:8]}...")
        
        # Add file to session
        add_file_to_session(session_id, file_id)
        print("✅ Added file to session")
        
        # Set as active file
        set_active_file(session_id, file_id)
        print("✅ Set as active file")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        return False
    
    # Step 3: Test file listing
    print("\n3️⃣ Testing file listing...")
    try:
        files = list_files(session_id)
        print(f"✅ Found {len(files)} files:")
        for file_info in files:
            print(f"   📄 {file_info.get('filename', 'Unknown')} ({file_info.get('file_id', 'No ID')[:8]}...)")
    except Exception as e:
        print(f"❌ Error listing files: {e}")
        return False
    
    # Step 4: Test active file retrieval
    print("\n4️⃣ Testing active file retrieval...")
    try:
        active_file_id = get_active_file_id(session_id)
        print(f"✅ Active file ID: {active_file_id[:8] if active_file_id else 'None'}...")
        
        if not active_file_id:
            print("❌ No active file found")
            return False
            
    except Exception as e:
        print(f"❌ Error getting active file: {e}")
        return False
    
    # Step 5: Test dataframe loading
    print("\n5️⃣ Testing dataframe loading...")
    try:
        loaded_df = get_dataframe(session_id, active_file_id)
        if loaded_df is not None:
            print(f"✅ Loaded dataframe: {loaded_df.shape}")
            print(f"   Columns: {list(loaded_df.columns)}")
            print(f"   Data types: {dict(loaded_df.dtypes)}")
        else:
            print("❌ Failed to load dataframe")
            return False
    except Exception as e:
        print(f"❌ Error loading dataframe: {e}")
        return False
    
    # Step 6: Test chart analysis (simulating available-charts endpoint)
    print("\n6️⃣ Testing chart analysis...")
    try:
        import numpy as np
        
        # Identify numeric and categorical columns
        numeric_cols = loaded_df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = loaded_df.select_dtypes(include=['object', 'category']).columns.tolist()
        
        print(f"✅ Numeric columns: {numeric_cols}")
        print(f"✅ Categorical columns: {categorical_cols}")
        
        # Determine available charts
        available_charts = []
        if len(numeric_cols) >= 1 and len(categorical_cols) >= 1:
            available_charts.extend(["bar", "line"])
        if len(numeric_cols) >= 2:
            available_charts.extend(["scatter", "heatmap"])
        if len(numeric_cols) >= 1:
            available_charts.append("histogram")
        if len(categorical_cols) >= 1 and len(numeric_cols) >= 1:
            available_charts.append("pie")
            
        print(f"✅ Available charts: {available_charts}")
        
        if not available_charts:
            print("❌ No charts available")
            return False
            
    except Exception as e:
        print(f"❌ Error analyzing charts: {e}")
        return False
    
    print("\n🎉 All tests passed! The visualization system should work correctly.")
    return True

def main():
    """Run the test."""
    success = test_file_upload_and_visualization()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("💡 The file upload and visualization system is working correctly.")
    else:
        print("\n❌ Test failed!")
        print("💡 There are still issues with the file upload and visualization system.")

if __name__ == "__main__":
    main()
