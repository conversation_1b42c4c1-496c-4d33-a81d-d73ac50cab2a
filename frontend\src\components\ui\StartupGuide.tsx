'use client';

import { useState } from 'react';
import { Card } from './Card';

export function StartupGuide() {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[var(--excel-text-primary)]">Getting Started</h2>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <h3 className="text-yellow-800 font-medium mb-1">Backend Server Required</h3>
                  <p className="text-yellow-700 text-sm">
                    Make sure the backend server is running on port 8000 before using the application.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[var(--excel-text-primary)]">Quick Setup Steps:</h3>
              
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-[var(--excel-green)] text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <div>
                    <h4 className="font-medium text-[var(--excel-text-primary)]">Start Backend Server</h4>
                    <p className="text-sm text-[var(--excel-text-muted)]">Navigate to the backend directory and run:</p>
                    <code className="block bg-gray-100 p-2 rounded mt-1 text-sm">
                      cd backend<br/>
                      pip install -r requirements.txt<br/>
                      python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
                    </code>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-[var(--excel-green)] text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <div>
                    <h4 className="font-medium text-[var(--excel-text-primary)]">Create Account</h4>
                    <p className="text-sm text-[var(--excel-text-muted)]">Register a new account or login with existing credentials.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-[var(--excel-green)] text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <div>
                    <h4 className="font-medium text-[var(--excel-text-primary)]">Upload Data</h4>
                    <p className="text-sm text-[var(--excel-text-muted)]">Upload CSV or Excel files to start analyzing your data.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-[var(--excel-green)] text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                  <div>
                    <h4 className="font-medium text-[var(--excel-text-primary)]">Explore Features</h4>
                    <p className="text-sm text-[var(--excel-text-muted)]">Use data profiling, cleaning, machine learning, and visualization tools.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 className="text-blue-800 font-medium mb-1">Features</h3>
                  <ul className="text-blue-700 text-sm space-y-1">
                    <li>• User authentication with secure sessions</li>
                    <li>• User-specific data storage</li>
                    <li>• Multi-file upload support</li>
                    <li>• Data profiling and cleaning</li>
                    <li>• Machine learning capabilities</li>
                    <li>• Interactive data visualization</li>
                    <li>• AI-powered data analysis</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setIsVisible(false)}
                className="excel-button px-4 py-2 text-white font-medium rounded-lg"
              >
                Get Started
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
